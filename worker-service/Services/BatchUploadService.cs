using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Repositories;
using System.Text.Json;
using System.Text;

namespace VidCompressor.WorkerService.Services;

public class BatchUploadService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<BatchUploadService> _logger;
    private readonly HttpClient _httpClient;
    private readonly string _apiServerUrl;
    private readonly TimeSpan _batchInterval;

    public BatchUploadService(
        IServiceProvider serviceProvider,
        ILogger<BatchUploadService> logger,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient();
        _apiServerUrl = configuration["SignalR:ApiServerUrl"] ?? "http://localhost:5119";
        _batchInterval = TimeSpan.FromSeconds(30); // Batch upload every 30 seconds
    }

    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Batch upload service starting");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        await base.StopAsync(cancellationToken);
        _httpClient?.Dispose();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Batch upload service started. Processing batches every {Interval} seconds", _batchInterval.TotalSeconds);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessBatchUploadsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch upload processing");
            }

            await Task.Delay(_batchInterval, stoppingToken);
        }
    }

    private async Task ProcessBatchUploadsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var compressionJobRepository = scope.ServiceProvider.GetRequiredService<ICompressionJobRepository>();
        var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();

        // Get jobs ready for batch upload
        var readyJobs = await compressionJobRepository.GetByStatusAsync(CompressionJobStatus.ReadyForBatchUpload);
        var jobsToUpload = readyJobs.Where(j => j.UploadToGooglePhotos && !string.IsNullOrEmpty(j.CompressedFilePath));

        // Group by user
        var groupedJobs = jobsToUpload.GroupBy(j => j.UserId);

        if (!groupedJobs.Any())
        {
            return; // No jobs ready for upload
        }

        _logger.LogInformation("Found {UserCount} users with jobs ready for batch upload", groupedJobs.Count());

        foreach (var userGroup in groupedJobs)
        {
            var userId = userGroup.Key;
            var userJobs = userGroup.ToList();

            _logger.LogInformation("Processing batch upload for user {UserId} with {JobCount} jobs", userId, userJobs.Count);

            try
            {
                await ProcessUserBatchUploadAsync(compressionJobRepository, userRepository, googlePhotosService, userId, userJobs, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process batch upload for user {UserId}", userId);
                
                // Mark all jobs in this batch as failed
                foreach (var job in userJobs)
                {
                    await MarkJobAsFailed(compressionJobRepository, job, $"Batch upload failed: {ex.Message}");
                }
            }
        }

        // Jobs are already saved individually in the loop above
    }

    private async Task ProcessUserBatchUploadAsync(
        ICompressionJobRepository compressionJobRepository,
        IUserRepository userRepository,
        GooglePhotosService googlePhotosService,
        string userId,
        List<CompressionJob> jobs,
        CancellationToken cancellationToken)
    {
        // Get user's access token
        var accessToken = await GetUserAccessToken(userRepository, userId);
        if (string.IsNullOrEmpty(accessToken))
        {
            _logger.LogError("Unable to get access token for user {UserId}", userId);
            foreach (var job in jobs)
            {
                await MarkJobAsFailed(compressionJobRepository, job, "Unable to get valid access token");
            }
            return;
        }

        // Update all jobs to uploading status
        foreach (var job in jobs)
        {
            job.Status = CompressionJobStatus.UploadingToGooglePhotos;
            await SendStatusUpdate(job, "Uploading to Google Photos");
            await compressionJobRepository.UpdateAsync(job);
        }

        try
        {
            // Convert CompressionJob to BatchUploadItem
            var batchItems = jobs.Select(job => new VidCompressor.Services.BatchUploadItem
            {
                Id = job.Id,
                CompressedFilePath = job.CompressedFilePath ?? string.Empty,
                MediaType = (int)job.MediaType // Convert enum to int
            }).ToList();

            // Perform batch upload and get the Google Photos URLs
            var uploadedUrls = await googlePhotosService.BatchUploadAsync(accessToken, batchItems, cancellationToken);

            // Mark all jobs as completed and set their Google Photos URLs
            foreach (var job in jobs)
            {
                job.Status = CompressionJobStatus.Completed;
                job.CompletedAt = DateTime.UtcNow;

                // Set the compressed Google Photos URL if available
                if (uploadedUrls.TryGetValue(job.Id, out var googlePhotosUrl))
                {
                    job.CompressedGooglePhotosUrl = googlePhotosUrl;
                    _logger.LogInformation("Set Google Photos URL for job {JobId}: {Url}", job.Id, googlePhotosUrl);
                }
                else
                {
                    _logger.LogWarning("No Google Photos URL returned for job {JobId}", job.Id);
                }

                await SendStatusUpdate(job, "Compression completed");
                await compressionJobRepository.UpdateAsync(job);

                // Keep compressed file for downloads - don't delete immediately
                _logger.LogInformation("Keeping compressed file for downloads: {FilePath} for job {JobId}",
                    job.CompressedFilePath, job.Id);
            }

            _logger.LogInformation("Successfully completed batch upload for user {UserId} with {JobCount} jobs", 
                userId, jobs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Batch upload failed for user {UserId}", userId);
            foreach (var job in jobs)
            {
                await MarkJobAsFailed(compressionJobRepository, job, $"Batch upload failed: {ex.Message}");
            }
        }
    }

    private async Task<string?> GetUserAccessToken(IUserRepository userRepository, string userId)
    {
        var user = await userRepository.GetByIdAsync(userId);
        if (user?.GoogleRefreshToken == null)
        {
            return null;
        }

        // TODO: Implement token refresh logic if needed
        // For now, assume GoogleAccessToken is still valid
        return user.GoogleAccessToken;
    }

    private async Task MarkJobAsFailed(ICompressionJobRepository compressionJobRepository, CompressionJob job, string errorMessage)
    {
        job.Status = CompressionJobStatus.Failed;
        job.ErrorMessage = errorMessage;
        job.CompletedAt = DateTime.UtcNow;

        await SendStatusUpdate(job, $"Failed: {errorMessage}");

        // Refund credits if they were deducted for this job
        if (job.CreditsUsed.HasValue && job.CreditsUsed.Value > 0)
        {
            using var scope = _serviceProvider.CreateScope();
            var creditsService = scope.ServiceProvider.GetRequiredService<CreditsService>();

            var mediaTypeText = job.MediaType == MediaType.Photo ? "photo" : "video";
            var refundSuccess = await creditsService.RefundCreditsAsync(
                job.UserId,
                job.CreditsUsed.Value,
                $"Refund for failed {mediaTypeText} upload ({job.Quality} quality)",
                job.Id);

            if (refundSuccess)
            {
                _logger.LogInformation("Refunded {Credits} credits to user {UserId} for failed upload job {JobId}",
                    job.CreditsUsed.Value, job.UserId, job.Id);
            }
            else
            {
                _logger.LogError("Failed to refund {Credits} credits to user {UserId} for failed upload job {JobId}",
                    job.CreditsUsed.Value, job.UserId, job.Id);
            }
        }

        // Keep compressed file for potential downloads even on failure
        _logger.LogInformation("Keeping compressed file for potential downloads: {FilePath} for failed job {JobId}",
            job.CompressedFilePath, job.Id);
    }

    private async Task SendStatusUpdate(CompressionJob job, string message)
    {
        try
        {
            var updateRequest = new
            {
                UserId = job.UserId,
                JobId = job.Id,
                MediaItemId = job.MediaItemId,
                Status = job.Status.ToString(),
                Message = message,
                Progress = GetProgressPercentage(job.Status, job.MediaType)
            };

            var json = JsonSerializer.Serialize(updateRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_apiServerUrl}/api/internal/compression-update", content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Status update sent successfully for job {JobId}", job.Id);
            }
            else
            {
                _logger.LogWarning("Failed to send status update for job {JobId}. Status: {StatusCode}",
                    job.Id, response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send status update for job {JobId}", job.Id);
        }
    }

    private static int GetProgressPercentage(CompressionJobStatus status, MediaType mediaType)
    {
        // Different progress flows for photos vs videos
        if (mediaType == MediaType.Photo)
        {
            return status switch
            {
                CompressionJobStatus.Queued => 0,
                CompressionJobStatus.DownloadingFromGooglePhotos => 20,
                // These shouldn't happen for photos, but if they do, maintain reasonable progress
                CompressionJobStatus.UploadingToStorage => 30,
                CompressionJobStatus.CompressingImage => 50,
                CompressionJobStatus.DownloadingFromStorage => 60,
                CompressionJobStatus.ReadyForBatchUpload => 70,
                CompressionJobStatus.UploadingToGooglePhotos => 90,
                CompressionJobStatus.Completed => 100,
                CompressionJobStatus.Failed => 0,
                CompressionJobStatus.Cancelled => 0,
                // For any unrecognized status, return 50% to avoid going backward
                _ => 50
            };
        }
        else // Video
        {
            return status switch
            {
                CompressionJobStatus.Queued => 0,
                CompressionJobStatus.DownloadingFromGooglePhotos => 10,
                CompressionJobStatus.UploadingToStorage => 20,
                CompressionJobStatus.TranscodingInProgress => 40,
                CompressionJobStatus.CompressingImage => 40, // Shouldn't happen for videos, but just in case
                CompressionJobStatus.DownloadingFromStorage => 60,
                CompressionJobStatus.ReadyForBatchUpload => 70,
                CompressionJobStatus.UploadingToGooglePhotos => 90,
                CompressionJobStatus.Completed => 100,
                CompressionJobStatus.Failed => 0,
                CompressionJobStatus.Cancelled => 0,
                // For any unrecognized status, return 50% to avoid going backward
                _ => 50
            };
        }
    }
}
