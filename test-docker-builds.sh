#!/bin/bash

# Test Docker builds for both services
echo "🔨 Testing Docker builds..."

echo "Building API Server..."
if docker build -t vidcompressor-api-server -f api-server/Dockerfile .; then
    echo "✅ API Server build successful"
else
    echo "❌ API Server build failed"
    exit 1
fi

echo ""
echo "Building Worker Service..."
if docker build -t vidcompressor-worker-service -f worker-service/Dockerfile .; then
    echo "✅ Worker Service build successful"
else
    echo "❌ Worker Service build failed"
    exit 1
fi

echo ""
echo "🎉 Individual Docker builds completed successfully!"

# Test with docker-compose
echo ""
echo "Testing docker-compose build..."
if docker-compose build; then
    echo "✅ Docker Compose build successful"
else
    echo "❌ Docker Compose build failed"
    exit 1
fi

echo ""
echo "🎉 All builds completed successfully!"
echo ""
echo "📋 Built images:"
echo "   - vidcompressor-api-server"
echo "   - vidcompressor-worker-service"
echo ""
echo "🚀 To start the full environment:"
echo "   docker-compose up -d"
