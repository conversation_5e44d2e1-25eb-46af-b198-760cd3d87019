version: '3.8'

services:
  firestore-emulator:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    ports:
      - "8080:8080"
    command: >
      sh -c "gcloud emulators firestore start --host-port=0.0.0.0:8080"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  api-server:
    build:
      context: .
      dockerfile: api-server/Dockerfile
    ports:
      - "5119:8080"
    depends_on:
      firestore-emulator:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Firestore__UseEmulator=true
      - Firestore__EmulatorHost=firestore-emulator:8080
      - ConnectionStrings__Redis=redis
      - GoogleCloud__CloudTasks__HandlerUrl=http://worker-service:8080

  worker-service:
    build:
      context: .
      dockerfile: worker-service/Dockerfile
    ports:
      - "5120:8080"
    depends_on:
      firestore-emulator:
        condition: service_healthy
      redis:
        condition: service_healthy
      api-server:
        condition: service_started
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Firestore__UseEmulator=true
      - Firestore__EmulatorHost=firestore-emulator:8080
      - ConnectionStrings__Redis=redis
      - SignalR__ApiServerUrl=http://api-server:8080
