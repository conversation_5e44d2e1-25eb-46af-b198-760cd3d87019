using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using VidCompressor.Data;
using VidCompressor.Services;
using VidCompressor.ApiServer.Middleware;
using VidCompressor.ApiServer.Hubs;
using VidCompressor.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Set Firestore emulator environment variable early
var firestoreConfig = builder.Configuration.GetSection("Firestore");
if (firestoreConfig.GetValue<bool>("UseEmulator"))
{
    var emulatorHost = firestoreConfig.GetValue<string>("EmulatorHost");
    if (!string.IsNullOrEmpty(emulatorHost))
    {
        Environment.SetEnvironmentVariable("FIRESTORE_EMULATOR_HOST", emulatorHost);
        Console.WriteLine($"[API-SERVER] Set FIRESTORE_EMULATOR_HOST to: {emulatorHost}");
    }
}

// Add services to the container.
builder.Services.AddFirestore(builder.Configuration);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Secret"] ?? throw new InvalidOperationException("JWT Secret not configured"))),
        ValidateIssuer = false,
        ValidateAudience = false
    };

    // Configure JWT for SignalR
    options.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
    {
        OnMessageReceived = context =>
        {
            var accessToken = context.Request.Query["access_token"];
            var path = context.HttpContext.Request.Path;

            // If the request is for our SignalR hub and we have a token
            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/notificationHub"))
            {
                context.Token = accessToken;
            }

            return Task.CompletedTask;
        }
    };
});

// Configure Google Cloud settings
builder.Services.Configure<GoogleCloudConfig>(builder.Configuration.GetSection("GoogleCloud"));

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        builder =>
        {
            builder.WithOrigins("http://localhost:3000") // URL of the React app
                   .AllowAnyHeader()
                   .AllowAnyMethod()
                   .AllowCredentials(); // Required for SignalR
        });
});

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddHttpClient();

// Add shared services
builder.Services.AddScoped<GooglePhotosService>();
builder.Services.AddScoped<GoogleCloudStorageService>();
builder.Services.AddScoped<GoogleTranscoderService>();
builder.Services.AddScoped<CloudTasksService>();
builder.Services.AddScoped<CreditsService>();
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add global exception handling
app.UseMiddleware<GlobalExceptionMiddleware>();

app.UseHttpsRedirection();

app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHub<NotificationHub>("/notificationHub");

app.Run();
