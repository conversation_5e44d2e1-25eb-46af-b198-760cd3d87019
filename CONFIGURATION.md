# Configuration Guide for Separated Architecture

## Overview

The backend has been separated into two services with specific configuration requirements:

- **API Server** (Port 5119): Handles frontend requests, authentication, payments
- **Worker Service** (Port 5120): Handles background processing, compression, transcoding

## Configuration Structure

### API Server (`api-server/`)

**Development** (`appsettings.Development.json`):
- Database connection
- Google OAuth (ClientId, RedirectUri)
- Google Cloud Tasks (points to worker service at localhost:5120)
- Stripe payment configuration
- JWT secret

**Production** (`appsettings.json`):
- Same as development but with production URLs
- Cloud Tasks HandlerUrl points to worker service Cloud Run URL
- Production Stripe keys

### Worker Service (`worker-service/`)

**Development** (`appsettings.Development.json`):
- Database connection
- Full Google Cloud configuration (buckets, transcoder, tasks)
- SignalR connection to API server (localhost:5119)

**Production** (`appsettings.json`):
- Same Google Cloud configuration with production values
- SignalR connection to API server Cloud Run URL
- Service account key path for authentication

### Shared Configuration

Both services share:
- Database connection string
- Google Cloud project settings
- Logging configuration

## Key Differences from Original Backend

1. **Cloud Tasks**: API server creates tasks, worker service processes them
2. **SignalR**: Worker service connects as client to API server hub
3. **Service URLs**: Each service has its own URL in development and production
4. **Responsibilities**: Clear separation of frontend vs background processing configs

## Environment Variables

For local development, the following environment variables are set in launch profiles:
- `ASPNETCORE_URLS`: Service-specific ports
- `SignalR__ApiServerUrl`: Worker service connection to API server
- `ASPNETCORE_ENVIRONMENT`: Development

## Production Deployment

When deploying to Google Cloud Run:
1. Update `HandlerUrl` in API server to point to worker service URL
2. Update `ApiServerUrl` in worker service to point to API server URL
3. Ensure service account keys are properly mounted
4. Configure environment-specific bucket names and project IDs
